package com.fishing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fishing.domain.User;
import com.fishing.domain.moment.MomentLike;
import com.fishing.mapper.MomentLikeMapper;
import com.fishing.mapper.UserMapper;
import com.fishing.service.MomentLikeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 动态点赞服务实现类
 */
@Service
@RequiredArgsConstructor
public class MomentLikeServiceImpl extends ServiceImpl<MomentLikeMapper, MomentLike> implements MomentLikeService {

    private final UserMapper userMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleLike(Long momentId, Long userId, boolean isLike) {
        // 检查是否已经点赞
        LambdaQueryWrapper<MomentLike> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentLike::getMomentId, momentId)
               .eq(MomentLike::getUserId, userId);
        
        MomentLike like = getOne(wrapper);
        
        if (isLike) {
            // 点赞操作
            if (like == null) {
                MomentLike newLike = new MomentLike();
                newLike.setMomentId(momentId);
                newLike.setUserId(userId);
                newLike.setCreatedAt(LocalDateTime.now());
                
                return save(newLike);
            }
            return true; // 已经点赞了，返回成功
        } else {
            // 取消点赞操作
            if (like != null) {
                return remove(wrapper);
            }
            return true; // 本来就没点赞，返回成功
        }
    }

    @Override
    public boolean isLiked(Long momentId, Long userId) {
        LambdaQueryWrapper<MomentLike> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentLike::getMomentId, momentId)
               .eq(MomentLike::getUserId, userId);
        
        return count(wrapper) > 0;
    }

    @Override
    public Integer getLikeCount(Long momentId) {
        LambdaQueryWrapper<MomentLike> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentLike::getMomentId, momentId);
        
        return Math.toIntExact(count(wrapper));
    }

    @Override
    public Page<User> getLikeUsers(Long momentId, Page<User> page) {
        // 先查询点赞记录
        LambdaQueryWrapper<MomentLike> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentLike::getMomentId, momentId)
               .orderByDesc(MomentLike::getCreatedAt);
        
        Page<MomentLike> likePage = new Page<>(page.getCurrent(), page.getSize());
        likePage = page(likePage, wrapper);
        
        // 创建空的用户分页对象
        Page<User> userPage = new Page<>();
        userPage.setCurrent(likePage.getCurrent());
        userPage.setSize(likePage.getSize());
        userPage.setTotal(likePage.getTotal());
        
        List<MomentLike> records = likePage.getRecords();
        if (records.isEmpty()) {
            userPage.setRecords(new ArrayList<>());
            return userPage;
        }
        
        // 获取用户ID列表
        List<Long> userIds = records.stream()
                .map(MomentLike::getUserId)
                .collect(Collectors.toList());
        
        // 批量查询用户信息
        List<User> users = userMapper.selectBatchIds(userIds);
        
        // 确保顺序与点赞记录一致
        List<User> sortedUsers = new ArrayList<>(userIds.size());
        for (Long userId : userIds) {
            users.stream()
                 .filter(user -> user.getId().equals(userId))
                 .findFirst()
                 .ifPresent(sortedUsers::add);
        }
        
        userPage.setRecords(sortedUsers);
        return userPage;
    }

    @Override
    public List<Long> batchGetLikeStatus(List<Long> momentIds, Long userId) {
        if (momentIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 批量查询用户点赞状态
        LambdaQueryWrapper<MomentLike> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MomentLike::getMomentId, momentIds)
               .eq(MomentLike::getUserId, userId);

        List<MomentLike> likes = list(wrapper);

        // 提取已点赞的动态ID
        return likes.stream()
                .map(MomentLike::getMomentId)
                .collect(Collectors.toList());
    }

    @Override
    public Map<Long, Integer> batchGetLikeCounts(List<Long> momentIds) {
        if (momentIds.isEmpty()) {
            return new HashMap<>();
        }

        // 批量查询点赞数
        LambdaQueryWrapper<MomentLike> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MomentLike::getMomentId, momentIds);

        List<MomentLike> likes = list(wrapper);

        // 按动态ID分组并计算点赞数
        return likes.stream()
                .collect(Collectors.groupingBy(
                    MomentLike::getMomentId,
                    Collectors.collectingAndThen(
                        Collectors.counting(),
                        Math::toIntExact
                    )
                ));
    }
}