package com.fishing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fishing.domain.moment.CommentVote;
import com.fishing.mapper.CommentVoteMapper;
import com.fishing.service.CommentVoteService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 评论投票服务实现类
 */
@Service
@RequiredArgsConstructor
public class CommentVoteServiceImpl extends ServiceImpl<CommentVoteMapper, CommentVote> implements CommentVoteService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean toggleVote(Long commentId, Long userId, Integer voteType) {
        // 查询用户是否已经投票
        LambdaQueryWrapper<CommentVote> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CommentVote::getCommentId, commentId)
               .eq(CommentVote::getUserId, userId);
        
        CommentVote existingVote = getOne(wrapper);
        
        if (existingVote == null) {
            // 用户未投票，创建新投票
            CommentVote newVote = new CommentVote();
            newVote.setCommentId(commentId);
            newVote.setUserId(userId);
            newVote.setVoteType(voteType);
            newVote.setCreatedAt(LocalDateTime.now());
            newVote.setUpdatedAt(LocalDateTime.now());
            
            return save(newVote);
        } else if (existingVote.getVoteType().equals(voteType)) {
            // 用户已经投了相同类型的票，取消投票
            return remove(wrapper);
        } else {
            // 用户投了不同类型的票，更新投票类型
            existingVote.setVoteType(voteType);
            existingVote.setUpdatedAt(LocalDateTime.now());
            
            return updateById(existingVote);
        }
    }

    @Override
    public Map<Long, Map<String, Integer>> getVoteStatsByCommentIds(List<Long> commentIds) {
        if (commentIds == null || commentIds.isEmpty()) {
            return new HashMap<>();
        }
        
        List<Map<String, Object>> results = baseMapper.getVoteStatsByCommentIds(commentIds);
        
        return results.stream().collect(Collectors.toMap(
            result -> ((Number) result.get("comment_id")).longValue(),
            result -> {
                Map<String, Integer> stats = new HashMap<>();
                stats.put("upVotes", ((Number) result.get("up_votes")).intValue());
                stats.put("downVotes", ((Number) result.get("down_votes")).intValue());
                return stats;
            }
        ));
    }

    @Override
    public Map<Long, Integer> getUserVotesByCommentIds(List<Long> commentIds, Long userId) {
        if (commentIds == null || commentIds.isEmpty() || userId == null) {
            return new HashMap<>();
        }
        
        List<Map<String, Object>> results = baseMapper.getUserVotesByCommentIds(commentIds, userId);
        
        return results.stream().collect(Collectors.toMap(
            result -> ((Number) result.get("comment_id")).longValue(),
            result -> ((Number) result.get("vote_type")).intValue()
        ));
    }

    @Override
    public Map<String, Integer> getVoteStatsByCommentId(Long commentId) {
        Map<String, Object> result = baseMapper.getVoteStatsByCommentId(commentId);
        
        Map<String, Integer> stats = new HashMap<>();
        if (result != null) {
            stats.put("upVotes", result.get("up_votes") != null ? ((Number) result.get("up_votes")).intValue() : 0);
            stats.put("downVotes", result.get("down_votes") != null ? ((Number) result.get("down_votes")).intValue() : 0);
        } else {
            stats.put("upVotes", 0);
            stats.put("downVotes", 0);
        }
        
        return stats;
    }

    @Override
    public Integer getUserVoteType(Long commentId, Long userId) {
        if (commentId == null || userId == null) {
            return null;
        }
        
        return baseMapper.getUserVoteType(commentId, userId);
    }
}
