package com.fishing.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fishing.domain.moment.CommentVote;

import java.util.List;
import java.util.Map;

/**
 * 评论投票服务接口
 */
public interface CommentVoteService extends IService<CommentVote> {

    /**
     * 投票或取消投票
     * @param commentId 评论ID
     * @param userId 用户ID
     * @param voteType 投票类型：1-点赞, -1-踩
     * @return 是否成功
     */
    boolean toggleVote(Long commentId, Long userId, Integer voteType);

    /**
     * 批量查询评论的投票统计
     * @param commentIds 评论ID列表
     * @return 投票统计结果，key为commentId，value为Map包含upVotes和downVotes
     */
    Map<Long, Map<String, Integer>> getVoteStatsByCommentIds(List<Long> commentIds);

    /**
     * 批量查询用户对评论的投票状态
     * @param commentIds 评论ID列表
     * @param userId 用户ID
     * @return 用户投票状态，key为commentId，value为voteType
     */
    Map<Long, Integer> getUserVotesByCommentIds(List<Long> commentIds, Long userId);

    /**
     * 查询单个评论的投票统计
     * @param commentId 评论ID
     * @return 投票统计结果
     */
    Map<String, Integer> getVoteStatsByCommentId(Long commentId);

    /**
     * 查询用户对评论的投票状态
     * @param commentId 评论ID
     * @param userId 用户ID
     * @return 投票类型，null表示未投票
     */
    Integer getUserVoteType(Long commentId, Long userId);
}
