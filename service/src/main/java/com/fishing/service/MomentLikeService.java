package com.fishing.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.fishing.domain.User;
import com.fishing.domain.moment.MomentLike;

import java.util.List;
import java.util.Map;

/**
 * 动态点赞服务接口
 */
public interface MomentLikeService extends IService<MomentLike> {

    /**
     * 点赞/取消点赞动态
     *
     * @param momentId 动态ID
     * @param userId   用户ID
     * @param isLike   true-点赞, false-取消点赞
     * @return 是否操作成功
     */
    boolean toggleLike(Long momentId, Long userId, boolean isLike);

    /**
     * 检查用户是否点赞了动态
     *
     * @param momentId 动态ID
     * @param userId   用户ID
     * @return 是否已点赞
     */
    boolean isLiked(Long momentId, Long userId);

    /**
     * 获取动态点赞数
     *
     * @param momentId 动态ID
     * @return 点赞数
     */
    Integer getLikeCount(Long momentId);

    /**
     * 获取动态点赞用户列表
     *
     * @param momentId 动态ID
     * @param page     分页参数
     * @return 点赞用户列表
     */
    Page<User> getLikeUsers(Long momentId, Page<User> page);

    /**
     * 批量获取动态点赞状态
     *
     * @param momentIds 动态ID列表
     * @param userId    用户ID
     * @return 动态ID到点赞状态的映射
     */
    List<Long> batchGetLikeStatus(List<Long> momentIds, Long userId);

    /**
     * 批量获取动态点赞数
     *
     * @param momentIds 动态ID列表
     * @return 动态ID到点赞数的映射
     */
    Map<Long, Integer> batchGetLikeCounts(List<Long> momentIds);
} 