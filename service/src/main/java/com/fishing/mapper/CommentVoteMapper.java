package com.fishing.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.fishing.domain.moment.CommentVote;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 评论投票Mapper接口
 */
@Mapper
public interface CommentVoteMapper extends BaseMapper<CommentVote> {

    /**
     * 批量查询评论的投票统计
     * @param commentIds 评论ID列表
     * @return 投票统计结果，key为commentId，value为Map包含upVotes和downVotes
     */
    @Select("<script>" +
            "SELECT comment_id, " +
            "SUM(CASE WHEN vote_type = 1 THEN 1 ELSE 0 END) as up_votes, " +
            "SUM(CASE WHEN vote_type = -1 THEN 1 ELSE 0 END) as down_votes " +
            "FROM comment_votes " +
            "WHERE comment_id IN " +
            "<foreach collection='commentIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "GROUP BY comment_id" +
            "</script>")
    List<Map<String, Object>> getVoteStatsByCommentIds(@Param("commentIds") List<Long> commentIds);

    /**
     * 批量查询用户对评论的投票状态
     * @param commentIds 评论ID列表
     * @param userId 用户ID
     * @return 用户投票状态，key为commentId，value为voteType
     */
    @Select("<script>" +
            "SELECT comment_id, vote_type " +
            "FROM comment_votes " +
            "WHERE comment_id IN " +
            "<foreach collection='commentIds' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach> " +
            "AND user_id = #{userId}" +
            "</script>")
    List<Map<String, Object>> getUserVotesByCommentIds(@Param("commentIds") List<Long> commentIds, @Param("userId") Long userId);

    /**
     * 查询单个评论的投票统计
     * @param commentId 评论ID
     * @return 投票统计结果
     */
    @Select("SELECT " +
            "SUM(CASE WHEN vote_type = 1 THEN 1 ELSE 0 END) as up_votes, " +
            "SUM(CASE WHEN vote_type = -1 THEN 1 ELSE 0 END) as down_votes " +
            "FROM comment_votes " +
            "WHERE comment_id = #{commentId}")
    Map<String, Object> getVoteStatsByCommentId(@Param("commentId") Long commentId);

    /**
     * 查询用户对评论的投票状态
     * @param commentId 评论ID
     * @param userId 用户ID
     * @return 投票类型，null表示未投票
     */
    @Select("SELECT vote_type FROM comment_votes WHERE comment_id = #{commentId} AND user_id = #{userId}")
    Integer getUserVoteType(@Param("commentId") Long commentId, @Param("userId") Long userId);
}
