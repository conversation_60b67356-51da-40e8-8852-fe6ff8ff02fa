import 'package:dart_mappable/dart_mappable.dart';
import 'package:user_app/models/user.dart';

part 'moment_vo.mapper.dart';

@MappableClass()
class MomentImageVo with MomentImageVoMappable {
  final int id;
  @MappableField(key: 'moment_id')
  final int momentId;
  @MappableField(key: 'image_url')
  final String imageUrl;
  @MappableField(key: 'display_order')
  final int displayOrder;

  const MomentImageVo({
    required this.id,
    required this.momentId,
    required this.imageUrl,
    required this.displayOrder,
  });

  static final fromMap = MomentImageVoMapper.fromMap;
}

@MappableClass()
class MomentVo with MomentVoMappable {
  final int id;
  final int? userId;
  final String? content;
  final List<MomentImageVo>? images;
  @MappableField(key: 'moment_type')
  final String? momentType;
  @MappableField(key: 'type_specific_data')
  final String? typeSpecificData;
  final String? tag;
  final num? fishCatch;
  @MappableField(key: 'fishing_spot_id')
  final int? fishingSpotId;
  @MappableField(key: 'fishing_spot_name')
  final String? fishingSpotName;
  final double? longitude;
  final double? latitude;
  final String? province;
  final String? city;
  final String? county;
  final String? addressDetail;
  final int? likeCount;
  final int numberOfDislikes;
  final int? status;
  final int? follows;
  final bool? followed;
  @MappableField(key: 'isLiked')
  final bool? liked;
  final bool? disliked;
  @MappableField(key: 'isBookmarked')
  final bool? bookmarked;
  @MappableField(key: 'created_at')
  final String? createdAt;
  final String? updateTime;
  final User publisher;
  @MappableField(key: 'number_of_comments')
  final num numberOfComments;
  @MappableField(key: 'follow_count')
  final num followCount;
  @MappableField(key: 'attention_count')
  final num attentionCount;
  @MappableField(key: 'moment_count')
  final num momentCount;
  final num commentCount;
  @MappableField(key: 'view_count')
  final num viewCount;

  const MomentVo({
    required this.id,
    this.userId,
    this.content,
    this.images,
    this.momentType,
    this.typeSpecificData,
    this.tag,
    this.fishCatch,
    this.fishingSpotId,
    this.fishingSpotName,
    this.longitude,
    this.latitude,
    this.province,
    this.city,
    this.county,
    this.addressDetail,
    this.likeCount,
    this.numberOfDislikes = 0,
    this.status,
    this.follows,
    this.followed,
    this.liked,
    this.disliked,
    this.bookmarked,
    this.createdAt,
    this.updateTime,
    required this.publisher,
    this.numberOfComments = 0,
    this.followCount = 0,
    this.attentionCount = 0,
    this.momentCount = 0,
    this.commentCount = 0,
    this.viewCount = 0,
  });

  // Convenience getters for backward compatibility
  List<String>? get pictures => images?.map((img) => img.imageUrl).toList();
  int get numberOfLikes => likeCount ?? 0;
  DateTime? get createTime =>
      createdAt != null ? DateTime.tryParse(createdAt!) : null;

  // Location getter - combines address components into a formatted string
  String? get location {
    if (province == null &&
        city == null &&
        county == null &&
        addressDetail == null) {
      return null;
    }

    final parts = <String>[];
    if (province != null && province!.isNotEmpty) parts.add(province!);
    if (city != null && city!.isNotEmpty && city != province) parts.add(city!);
    if (county != null && county!.isNotEmpty) parts.add(county!);
    if (addressDetail != null && addressDetail!.isNotEmpty) {
      parts.add(addressDetail!);
    }

    return parts.isEmpty ? null : parts.join(' ');
  }

  // Tags getter - splits the tag string into a list
  List<String>? get tags {
    if (tag == null || tag!.isEmpty) return null;
    return tag!
        .split(',')
        .map((e) => e.trim())
        .where((e) => e.isNotEmpty)
        .toList();
  }

  static final fromMap = MomentVoMapper.fromMap;
}
