import 'package:algoliasearch/algoliasearch_lite.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/services/moment_service.dart';

class SearchService {
  static const String indexName = 'moments';
  final SearchClient searchClient;
  final MomentService? momentService;

  SearchService(this.searchClient, {this.momentService});

  /// 搜索动态
  ///
  /// [query] - 搜索关键词
  /// [page] - 页码 (0-based)
  /// [pageSize] - 每页数量
  Future<(List<MomentVo>, int?, int?)> searchMoments(
      String query, int page, int pageSize) async {
    final queryHits = SearchForHits(
        indexName: indexName, query: query, hitsPerPage: pageSize, page: page);
    final responseHits = await searchClient.searchIndex(request: queryHits);

    // Process search results
    for (var hit in responseHits.hits) {
      if (hit['pictures'] == null) {
        hit['pictures'] = [];
      }
      if (hit['pictures'] != null && hit['pictures'] is String) {
        hit['pictures'] = hit['pictures'].toString().split(',');
      }
      // Don't hardcode like status - let the backend provide the correct status
      // The backend should include user-specific data when available
      if (!hit.containsKey('isLiked')) {
        hit['isLiked'] = false;
      }
      if (!hit.containsKey('disliked')) {
        hit['disliked'] = false;
      }
    }

    var moments =
        responseHits.hits.map((hit) => MomentVo.fromMap(hit)).toList();

    // If we have a moment service, we could fetch updated like status here
    // but for performance reasons, it's better to have the search index
    // include user-specific data or fetch it on the backend

    var totalPage = responseHits.nbPages;
    var currentPage = responseHits.page;
    return (moments, totalPage, currentPage);
  }
}
