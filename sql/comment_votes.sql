-- 评论投票表
DROP TABLE IF EXISTS `comment_votes`;
CREATE TABLE `comment_votes`
(
    `id`         bigint   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `comment_id` bigint   NOT NULL COMMENT '评论ID',
    `user_id`    bigint   NOT NULL COMMENT '用户ID',
    `vote_type`  tinyint  NOT NULL COMMENT '投票类型：1-点赞, -1-踩',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_comment_user` (`comment_id`, `user_id`),
    KEY `idx_comment_id` (`comment_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_vote_type` (`vote_type`),
    KEY `idx_created_at` (`created_at`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='评论投票表';

-- 创建索引优化查询性能
CREATE INDEX `idx_comment_votes_comment_type` ON `comment_votes` (`comment_id`, `vote_type`);
CREATE INDEX `idx_comment_votes_user_created` ON `comment_votes` (`user_id`, `created_at` DESC);

