package com.fishing.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fishing.domain.moment.MomentComment;
import com.fishing.dto.moment.MomentCommentDTO;
import com.fishing.service.CommentVoteService;
import com.fishing.service.MomentCommentService;
import com.fishing.util.JwtTokenUtil;
import com.fishing.vo.ApiResponse;
import com.fishing.vo.moment.MomentCommentResponse;
import com.fishing.vo.moment.MomentCommentVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

/**
 * 评论控制器
 */
@Tag(name = "评论管理", description = "评论相关接口")
@RestController
@RequestMapping("/comments")
@RequiredArgsConstructor
public class CommentController {

    private final MomentCommentService momentCommentService;
    private final CommentVoteService commentVoteService;
    private final JwtTokenUtil jwtTokenUtil;

    @GetMapping("/list/{momentId}")
    @Operation(summary = "获取动态评论列表")
    public ResponseEntity<ApiResponse<Page<MomentCommentVO>>> getComments(
            @PathVariable Long momentId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            Authentication authentication) {

        // 获取当前用户ID（可能为空，支持未登录用户访问）
        Long currentUserId = null;
        if (authentication != null) {
            try {
                currentUserId = jwtTokenUtil.getUserId(authentication);
            } catch (Exception e) {
                // 忽略认证异常，允许未登录用户访问
                currentUserId = null;
            }
        }

        Page<MomentComment> pageParam = new Page<>(page, size);
        Page<MomentCommentVO> comments = momentCommentService.getCommentsByMomentId(momentId, pageParam, currentUserId);
        return ResponseEntity.ok(ApiResponse.success(comments));
    }

    @PostMapping("/create")
    @Operation(summary = "创建评论")
    public ResponseEntity<ApiResponse<MomentCommentResponse>> createComment(
            @RequestBody @Valid MomentCommentDTO dto,
            Authentication authentication) {
        Long userId = jwtTokenUtil.getUserId(authentication);
        MomentComment comment = new MomentComment();
        comment.setMomentId(dto.getMomentId());
        comment.setUserId(userId);
        comment.setContent(dto.getContent());
        comment.setParentId(dto.getParentId());

        Long commentId = momentCommentService.addComment(comment);

        return ResponseEntity.ok(ApiResponse.success(new MomentCommentResponse(commentId)));
    }

    @GetMapping("/{commentId}/replies")
    @Operation(summary = "获取评论回复列表")
    public ResponseEntity<ApiResponse<Page<MomentCommentVO>>> getReplies(
            @PathVariable Long commentId,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            Authentication authentication) {

        // 获取当前用户ID（可能为空，支持未登录用户访问）
        Long currentUserId = null;
        if (authentication != null) {
            try {
                currentUserId = jwtTokenUtil.getUserId(authentication);
            } catch (Exception e) {
                // 忽略认证异常，允许未登录用户访问
                currentUserId = null;
            }
        }

        Page<MomentComment> pageParam = new Page<>(page, size);
        Page<MomentCommentVO> replies = momentCommentService.getRepliesByCommentId(commentId, pageParam, currentUserId);
        return ResponseEntity.ok(ApiResponse.success(replies));
    }

    @PostMapping("/{commentId}/vote/{voteType}")
    @Operation(summary = "评论投票")
    public ResponseEntity<ApiResponse<Void>> voteComment(
            @PathVariable Long commentId,
            @PathVariable String voteType,
            Authentication authentication) {
        Long userId = jwtTokenUtil.getUserId(authentication);

        // 解析投票类型
        int voteTypeValue;
        try {
            // 首先尝试解析为数字
            voteTypeValue = Integer.parseInt(voteType);
            if (voteTypeValue != 1 && voteTypeValue != -1) {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error(400, "无效的投票类型数值: " + voteType + "，只支持 1（点赞）或 -1（踩）"));
            }
        } catch (NumberFormatException e) {
            // 如果不是数字，则按字符串解析
            switch (voteType.toLowerCase()) {
                case "up":
                case "like":
                    voteTypeValue = 1; // 点赞
                    break;
                case "down":
                case "dislike":
                    voteTypeValue = -1; // 踩
                    break;
                default:
                    return ResponseEntity.badRequest()
                            .body(ApiResponse.error(400, "无效的投票类型: " + voteType));
            }
        }

        try {
            boolean success = commentVoteService.toggleVote(commentId, userId, voteTypeValue);
            if (success) {
                return ResponseEntity.ok(ApiResponse.success(null));
            } else {
                return ResponseEntity.badRequest()
                        .body(ApiResponse.error(400, "投票失败"));
            }
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error(500, "投票失败: " + e.getMessage()));
        }
    }
}
